# 图标文件说明

## 需要添加的图标文件

### 底部导航栏图标
请在 `assets/icons` 目录下添加以下图标文件：

1. `home.png` - 首页未选中图标
2. `home-active.png` - 首页选中图标
3. `wishpool.png` - 许愿池未选中图标
4. `wishpool-active.png` - 许愿池选中图标
5. `profile.png` - 我的未选中图标
6. `profile-active.png` - 我的选中图标

### 其他图标
请在 `assets/images` 目录下添加以下图标文件：

1. `default-avatar.png` - 默认头像图标
2. `settings.png` - 设置图标
3. `feedback.png` - 反馈图标
4. `about.png` - 关于图标

## 图标规格建议

- 底部导航栏图标：建议尺寸 48x48 像素
- 其他功能图标：建议尺寸 64x64 像素
- 默认头像：建议尺寸 200x200 像素

## 临时解决方案

如果暂时没有合适的图标，可以使用微信小程序内置的图标库。在这种情况下，需要修改相关的 WXML 文件，将图片标签替换为图标组件。
