/* pages/wishpool/wishpool.wxss */
.container {
  padding: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.add-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 30rpx;
  line-height: 1.5;
}

.wish-list {
  width: 100%;
}

.wish-item {
  background-color: #f8f8f8;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.wish-content {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.wish-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
