// pages/wishpool/wishpool.js
Page({
  data: {
    wishes: [] // 存储许愿池中的愿望
  },

  onLoad: function() {
    // 页面加载时获取许愿池数据
    this.getWishPoolData();
  },

  onShow: function() {
    // 每次页面显示时刷新数据
    this.getWishPoolData();
  },

  // 获取许愿池数据
  getWishPoolData: function() {
    // 这里可以调用API获取许愿池数据
    // 示例数据
    const mockWishes = [
      { id: 1, content: '希望考试顺利通过', author: '用户A', createTime: '2023-05-01' },
      { id: 2, content: '希望工作顺利', author: '用户B', createTime: '2023-05-02' },
      { id: 3, content: '希望家人健康', author: '用户C', createTime: '2023-05-03' }
    ];
    
    this.setData({
      wishes: mockWishes
    });
  },

  // 添加新愿望
  addWish: function() {
    wx.navigateTo({
      url: '/pages/add-wish/add-wish'
    });
  }
});
