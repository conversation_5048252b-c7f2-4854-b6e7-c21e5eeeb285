/* pages/profile/profile.wxss */
.container {
  padding: 20rpx;
}

/* 用户信息区域样式 */
.user-info-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar, .default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.not-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.login-tip {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

.login-btn, .logout-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.login-btn {
  background-color: #07c160;
  color: #fff;
}

.logout-btn {
  background-color: #f5f5f5;
  color: #666;
}

/* 我的愿望区域样式 */
.my-wishes-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  border-left: 8rpx solid #07c160;
  padding-left: 20rpx;
}

.wish-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.wish-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.wish-item:last-child {
  border-bottom: none;
}

.wish-content {
  font-size: 30rpx;
  margin-bottom: 10rpx;
}

.wish-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.wish-time {
  color: #999;
}

.wish-status {
  color: #ff9800;
}

.wish-status.realized {
  color: #07c160;
}

.empty-tip {
  text-align: center;
  padding: 50rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 功能区域样式 */
.function-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.function-grid {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.33%;
  padding: 20rpx 0;
  box-sizing: border-box;
}

.function-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
}

.function-icon {
  width: 40rpx;
  height: 40rpx;
}

.function-text {
  font-size: 28rpx;
  color: #333;
  margin-top: 10rpx;
}
