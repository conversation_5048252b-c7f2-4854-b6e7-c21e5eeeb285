<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <block wx:if="{{hasUserInfo}}">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatarUrl || '/assets/images/default-avatar.png'}}" mode="cover"></image>
        <view class="user-detail">
          <text class="nickname">{{userInfo.nickName}}</text>
          <text class="user-id">ID: 88888888</text>
        </view>
      </view>
      <button class="logout-btn" bindtap="logout">退出登录</button>
    </block>
    <block wx:else>
      <view class="not-login">
        <image class="default-avatar" src="/assets/images/default-avatar.png" mode="cover"></image>
        <text class="login-tip">请登录查看个人信息</text>
      </view>
      <button class="login-btn" bindtap="getUserProfile">微信登录</button>
    </block>
  </view>

  <!-- 我的愿望区域 -->
  <view class="my-wishes-section" wx:if="{{hasUserInfo}}">
    <view class="section-header">
      <view class="section-title">我的愿望</view>
    </view>
    <view class="wish-list">
      <block wx:if="{{myWishes.length > 0}}">
        <view class="wish-item" wx:for="{{myWishes}}" wx:key="id">
          <view class="wish-content">{{item.content}}</view>
          <view class="wish-info">
            <text class="wish-time">{{item.createTime}}</text>
            <text class="wish-status {{item.status === '已实现' ? 'realized' : ''}}">{{item.status}}</text>
          </view>
        </view>
      </block>
      <view class="empty-tip" wx:else>
        <text>您还没有许过愿望哦~</text>
      </view>
    </view>
  </view>

  <!-- 功能区域 -->
  <view class="function-section">
    <view class="function-grid">
      <view class="function-item" bindtap="onSettingsTap">
        <view class="function-icon-wrapper">
          <image class="function-icon" src="/assets/images/settings.png"></image>
        </view>
        <text class="function-text">设置</text>
      </view>
      <view class="function-item" bindtap="onFeedbackTap">
        <view class="function-icon-wrapper">
          <image class="function-icon" src="/assets/images/feedback.png"></image>
        </view>
        <text class="function-text">反馈</text>
      </view>
      <view class="function-item" bindtap="onAboutTap">
        <view class="function-icon-wrapper">
          <image class="function-icon" src="/assets/images/about.png"></image>
        </view>
        <text class="function-text">关于</text>
      </view>
    </view>
  </view>
</view>
