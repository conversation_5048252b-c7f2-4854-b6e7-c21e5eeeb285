// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    myWishes: [] // 用户的愿望列表
  },

  onLoad: function() {
    // 检查是否可以使用 getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 检查登录状态
    this.checkLoginStatus()

    // 获取用户愿望列表
    this.getMyWishes()
  },

  onShow: function() {
    // 每次页面显示时检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const isLoggedIn = app.globalData.isLoggedIn
    const userInfo = wx.getStorageSync('userInfo')

    if (isLoggedIn && userInfo) {
      // 已登录
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    } else {
      // 未登录
      this.setData({
        hasUserInfo: false
      })
    }
  },

  // 获取用户信息
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        // 保存用户信息到本地
        wx.setStorageSync('userInfo', res.userInfo)

        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })

        // 获取登录凭证并登录
        this.login(res.userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        wx.showToast({
          title: '授权失败',
          icon: 'none'
        })
      }
    })
  },

  // 登录
  login: function(userInfo) {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取IP地址
          wx.getLocalIPAddress({
            success: (ipRes) => {
              // 发送登录请求
              wx.request({
                url: `${app.globalData.apiBaseUrl}/v1/auth/wechat/login`,
                method: 'POST',
                data: {
                  code: res.code,
                  userInfo: userInfo,
                  ipAddress: ipRes.localip
                },
                success: (loginRes) => {
                  if (loginRes.statusCode === 200 && loginRes.data.code === 10200) {
                    // 保存token
                    wx.setStorageSync('token', loginRes.data.data.access_token)
                    wx.setStorageSync('refresh_token', loginRes.data.data.refresh_token)
                    wx.setStorageSync('token_type', loginRes.data.data.token_type || 'Bearer')

                    // 更新全局数据
                    app.globalData.isLoggedIn = true
                    app.globalData.userInfo = userInfo

                    wx.showToast({
                      title: '登录成功',
                      icon: 'success'
                    })
                  } else {
                    wx.showToast({
                      title: '登录失败',
                      icon: 'none'
                    })
                  }
                },
                fail: () => {
                  wx.showToast({
                    title: '网络错误',
                    icon: 'none'
                  })
                }
              })
            },
            fail: () => {
              wx.showToast({
                title: '获取IP失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 获取我的愿望列表
  getMyWishes: function() {
    // 这里可以调用API获取用户的愿望列表
    // 示例数据
    const mockWishes = [
      { id: 1, content: '希望工作顺利', status: '已实现', createTime: '2023-05-01' },
      { id: 2, content: '希望旅行愉快', status: '待实现', createTime: '2023-05-10' }
    ];

    this.setData({
      myWishes: mockWishes
    });
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('token')
          wx.removeStorageSync('refresh_token')
          wx.removeStorageSync('userInfo')

          // 更新全局数据
          app.globalData.isLoggedIn = false
          app.globalData.userInfo = null

          // 更新页面状态
          this.setData({
            userInfo: null,
            hasUserInfo: false
          })

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 设置按钮点击处理
  onSettingsTap: function() {
    wx.showToast({
      title: '设置功能开发中',
      icon: 'none'
    })
  },

  // 反馈按钮点击处理
  onFeedbackTap: function() {
    wx.showToast({
      title: '反馈功能开发中',
      icon: 'none'
    })
  },

  // 关于按钮点击处理
  onAboutTap: function() {
    wx.showToast({
      title: '关于功能开发中',
      icon: 'none'
    })
  }
});
