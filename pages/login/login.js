Page({
    async onWeChatLogin() {
      try {
        const profile = await wx.getUserProfile({
          desc: '用于登录并完善资料',
        });
  
        const loginRes = await wx.login(); // 获取 code
        const code = loginRes.code;
        console.log(code)
  
        const { encryptedData, iv, rawData, signature } = profile;
  
        // 发送到后端
        wx.request({
          url: 'http://127.0.0.1:8000/v1/auth/wechat/login',
          method: 'POST',
          data: {
            code,
            encryptedData,
            iv,
            rawData,
            signature,
          },
          success: (res) => {
            wx.setStorageSync('token', res.data.token);
            wx.reLaunch({ url: '/pages/index/index' });
          },
          fail: () => {
            wx.showToast({ title: '登录失败', icon: 'none' });
          }
        });
  
      } catch (err) {
        console.error('用户拒绝授权或出错', err);
        wx.showToast({ title: '授权失败', icon: 'none' });
      }
    }
  });
  