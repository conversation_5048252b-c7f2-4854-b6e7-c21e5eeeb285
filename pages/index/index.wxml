<view class="container">
  <!-- 主页内容区域 -->
  <view class="main-content">
    <!-- 八八一下功能区 -->
    <view class="baba-section">
      <view class="baba-title">八八一下</view>
      <view class="baba-input-area">
        <textarea class="baba-input" placeholder="请输入您想要八卦的内容..." maxlength="200" bindinput="onInputChange"></textarea>
        <view class="baba-counter">{{inputLength}}/200</view>
      </view>
      <button class="baba-button" bindtap="onBabaSubmit">八八一下</button>
    </view>

    <!-- 八卦结果区 -->
    <view class="result-section" wx:if="{{showResult}}">
      <view class="result-title">八卦结果</view>
      <view class="result-content">
        <text>{{babaResult}}</text>
      </view>
      <view class="result-actions">
        <button class="action-btn share-btn" open-type="share">分享结果</button>
        <button class="action-btn save-btn" bindtap="saveToWishPool">存入许愿池</button>
      </view>
    </view>

    <!-- 历史记录区 -->
    <view class="history-section">
      <view class="history-title">历史八卦</view>
      <view class="history-list">
        <block wx:if="{{historyList.length > 0}}">
          <view class="history-item" wx:for="{{historyList}}" wx:key="id" bindtap="viewHistoryDetail" data-id="{{item.id}}">
            <view class="history-content">{{item.content}}</view>
            <view class="history-time">{{item.time}}</view>
          </view>
        </block>
        <view class="empty-tip" wx:else>
          <text>暂无历史记录</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 授权弹窗 -->
  <view class="auth-popup" wx:if="{{showAuthDialog}}">
    <view class="auth-mask" bindtap="closeAuthDialog"></view>
    <view class="auth-dialog">
      <view class="auth-title">微信授权登录</view>
      <view class="auth-content">
        <text>请授权获取您的微信头像和昵称</text>
        <text class="auth-tips">授权后将获取您的IP地址用于安全验证</text>
      </view>
      <view class="auth-buttons">
        <button class="auth-cancel" bindtap="closeAuthDialog">取消</button>
        <button class="auth-confirm" type="primary" open-type="getUserInfo" bindtap="onGetUserProfile">确认授权</button>
      </view>
    </view>
  </view>
</view>
