// index.js
const app = getApp()
const apiService = require('../../utils/api')
// 引入schema定义，用于类型提示和文档
require('../../utils/schema')

Page({
  /**
   * 页面数据
   * @type {import('../../utils/schema').PageData}
   */
  data: {
    userInfo: null, // 用户信息
    hasUserInfo: false, // 是否已有用户信息
    canIUseGetUserProfile: false, // 是否可以使用getUserProfile
    showAuthDialog: false, // 是否显示授权弹窗
    ipAddress: '', // IP地址

    // AI问卦功能相关数据
    showGuaModal: false, // 是否显示问卦输入弹窗
    showGuaResult: false, // 是否显示问卦结果弹窗
    guaQuestion: '', // 问卦问题
    guaQuestionLength: 0, // 问卦问题长度
    guaLoading: false, // 问卦加载状态
    guaButtonDisabled: false, // 问卦按钮禁用状态
    guaButtonCooldown: 0, // 按钮冷却倒计时
    guaResult: null, // 问卦结果
    guaUsageInfo: { // 问卦使用信息
      current: 0,
      remaining: 100,
      reset_time: ''
    },
    guaHistory: [] // 问卦历史记录
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function () {
    console.log('页面加载')

    // 检查是否可以使用 getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 获取IP地址
    this.getIPAddresses()

    // 获取问卦历史记录
    this.getGuaHistoryList()

    // 清除可能存在的错误登录状态
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    app.globalData.isLoggedIn = false

    // 强制显示授权弹窗
    this.setData({
      showAuthDialog: true
    })

    console.log('授权弹窗状态:', this.data.showAuthDialog)

    // 检查登录状态
    this.checkLoginStatus()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 每次页面显示时检查登录状态
    this.checkLoginStatus()
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const isLoggedIn = app.globalData.isLoggedIn
    /** @type {import('../../utils/schema').WechatUserInfo|null} */
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    console.log('页面检查登录状态:', { isLoggedIn, userInfo, token })

    if (isLoggedIn && userInfo && token) {
      // 已登录
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true,
        showAuthDialog: false
      })
      console.log('页面确认用户已登录，关闭授权弹窗')
    } else {
      // 未登录，显示授权弹窗
      this.setData({
        userInfo: null,
        hasUserInfo: false,
        showAuthDialog: true
      })
      console.log('页面确认用户未登录，显示授权弹窗')
    }
  },

  /**
   * 获取IP地址
   */
  getIPAddresses: function() {
    wx.getLocalIPAddress({
      success: (res) => {
        const localip = res.localip
        console.log('IP地址:', localip)
        this.setData({
          ipAddress: localip
        })

        // 同时更新全局数据
        app.globalData.ipAddress = localip
      },
      fail: (err) => {
        console.error('获取IP地址失败:', err)
      }
    })
  },

  /**
   * 关闭授权弹窗
   */
  closeAuthDialog: function() {
    this.setData({
      showAuthDialog: false
    })
  },

  /**
   * 点击授权按钮
   */
  onGetUserProfile: function() {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途
      success: (res) => {
        /** @type {import('../../utils/schema').WechatUserInfo} */
        const userInfo = res.userInfo
        console.log('用户信息', userInfo)

        // 保存用户信息到本地
        wx.setStorageSync('userInfo', userInfo)

        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        })

        // 获取登录凭证
        this.getLoginCode(userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败', err)
        wx.showToast({
          title: '授权失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 获取登录凭证code
   * @param {import('../../utils/schema').WechatUserInfo} userInfo - 用户信息
   */
  getLoginCode: function(userInfo) {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 发送用户信息和code到后端
          this.sendLoginInfoToBackend(res.code, userInfo)
        } else {
          console.error('登录失败', res)
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('获取登录凭证失败', err)
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 发送登录信息到后端
   * @param {string} code - 微信登录凭证
   * @param {import('../../utils/schema').WechatUserInfo} userInfo - 用户信息
   * @returns {Promise<void>}
   */
  sendLoginInfoToBackend: function(code, userInfo) {
    wx.showLoading({
      title: '登录中...',
    })

    /**
     * 准备登录数据
     * @type {import('../../utils/schema').WechatLoginRequest}
     */
    const loginData = {
      code: code,
      userInfo: userInfo,
      ipAddress: this.data.ipAddress
    }

    // 使用API服务发送登录请求
    return apiService.login(loginData)
      .then(/** @param {import('../../utils/schema').WechatLoginResponse} res */res => {
        wx.hideLoading()

        // 登录成功
        console.log('登录成功', res)

        if (res.code === 10200 && res.data && res.data.access_token) {
          // 保存token和用户信息
          wx.setStorageSync('token', res.data.access_token)
          wx.setStorageSync('refresh_token', res.data.refresh_token)
          wx.setStorageSync('token_type', res.data.token_type || 'Bearer')

          // 如果后端返回了用户信息，保存到本地
          if (res.data.user) {
            const serverUserInfo = {
              nickName: res.data.user.nickname || userInfo.nickName,
              avatarUrl: res.data.user.avatar_url || userInfo.avatarUrl,
              // 保留原有的其他用户信息
              gender: userInfo.gender,
              country: userInfo.country,
              province: userInfo.province,
              city: userInfo.city,
              language: userInfo.language
            }
            wx.setStorageSync('userInfo', serverUserInfo)

            // 更新页面显示
            this.setData({
              userInfo: serverUserInfo
            })
          }

          // 更新全局数据
          app.globalData.isLoggedIn = true
          app.globalData.userInfo = wx.getStorageSync('userInfo')

          // 关闭授权弹窗
          this.setData({
            showAuthDialog: false
          })

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
        } else {
          // 登录成功但返回数据格式不符合预期
          console.error('登录返回数据格式异常', res)
          wx.showToast({
            title: '登录异常',
            icon: 'none'
          })
        }
      })
      .catch(/** @param {Error} err */err => {
        wx.hideLoading()
        console.error('登录失败', err)
        wx.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        })
      })
  },

  /**
   * 显示问卦输入弹窗
   */
  showGuaModal: function() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      this.setData({
        showAuthDialog: true
      });
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 检查按钮冷却状态
    if (this.data.guaButtonDisabled) {
      wx.showToast({
        title: `请等待${this.data.guaButtonCooldown}秒`,
        icon: 'none'
      });
      return;
    }

    this.setData({
      showGuaModal: true,
      guaQuestion: '',
      guaQuestionLength: 0
    });
  },

  /**
   * 关闭问卦输入弹窗
   */
  closeGuaModal: function() {
    this.setData({
      showGuaModal: false
    });
  },

  /**
   * 关闭问卦结果弹窗
   */
  closeGuaResult: function() {
    this.setData({
      showGuaResult: false
    });
  },

  /**
   * 问卦输入内容变化处理
   * @param {Object} e - 事件对象
   */
  onGuaInputChange: function(e) {
    const content = e.detail.value;
    this.setData({
      guaQuestion: content,
      guaQuestionLength: content.length
    });
  },

  /**
   * 提交问卦请求
   */
  onGuaSubmit: function() {
    const question = this.data.guaQuestion;

    if (!question.trim()) {
      wx.showToast({
        title: '请输入问卦内容',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      this.setData({
        showAuthDialog: true
      });
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 启动按钮冷却
    this.startButtonCooldown();

    // 关闭输入弹窗
    this.setData({
      showGuaModal: false,
      guaLoading: true
    });

    // 显示加载中
    wx.showLoading({
      title: '问卦中...',
    });

    // 准备请求数据
    const requestData = {
      question: question
    };

    // 调用问卦API
    apiService.queryGua(requestData)
      .then(res => {
        wx.hideLoading();
        this.setData({
          guaLoading: false
        });

        if (res.code === 10200 && res.data) {
          // 更新使用次数信息
          this.setData({
            guaUsageInfo: {
              current: res.data.current,
              remaining: res.data.remaining,
              reset_time: res.data.reset_time
            }
          });

          // 保存到历史记录
          this.saveToGuaHistory(question, res.data);

          // 显示结果弹窗
          this.setData({
            showGuaResult: true,
            guaResult: res.data
          });

          console.log('问卦成功', res);
        } else {
          // 请求成功但返回错误
          wx.showToast({
            title: res.message || '问卦失败',
            icon: 'none'
          });
          console.error('问卦失败', res);
        }
      })
      .catch(err => {
        wx.hideLoading();
        this.setData({
          guaLoading: false
        });

        // 如果是认证错误，可能需要重新登录
        if (err.message.includes('登录已过期') || err.message.includes('认证失败')) {
          this.setData({
            showAuthDialog: true
          });
        }

        wx.showToast({
          title: err.message || '网络错误',
          icon: 'none'
        });
        console.error('问卦请求失败', err);
      });
  },

  /**
   * 启动按钮冷却
   */
  startButtonCooldown: function() {
    this.setData({
      guaButtonDisabled: true,
      guaButtonCooldown: 3
    });

    const timer = setInterval(() => {
      const cooldown = this.data.guaButtonCooldown - 1;
      if (cooldown <= 0) {
        clearInterval(timer);
        this.setData({
          guaButtonDisabled: false,
          guaButtonCooldown: 0
        });
      } else {
        this.setData({
          guaButtonCooldown: cooldown
        });
      }
    }, 1000);
  },

  /**
   * 保存到问卦历史记录
   * @param {string} question - 问卦问题
   * @param {Object} result - 问卦结果
   */
  saveToGuaHistory: function(question, result) {
    const now = new Date();
    const time = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    const newHistory = {
      id: Date.now().toString(),
      question: question,
      result: result,
      time: time
    };

    // 获取现有历史记录
    const guaHistory = this.data.guaHistory;

    // 添加新记录到开头
    guaHistory.unshift(newHistory);

    // 最多保存20条记录
    if (guaHistory.length > 20) {
      guaHistory.pop();
    }

    // 更新数据
    this.setData({
      guaHistory: guaHistory
    });

    // 保存到本地存储
    wx.setStorageSync('guaHistory', guaHistory);
  },

  /**
   * 获取问卦历史记录
   */
  getGuaHistoryList: function() {
    const guaHistory = wx.getStorageSync('guaHistory') || [];
    this.setData({
      guaHistory: guaHistory
    });
  },

  /**
   * 查看问卦历史记录详情
   * @param {Object} e - 事件对象
   */
  viewGuaHistoryDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const history = this.data.guaHistory.find(item => item.id === id);

    if (history) {
      this.setData({
        showGuaResult: true,
        guaResult: history.result,
        guaQuestion: history.question
      });
    }
  },



  /**
   * 生成八卦结果（模拟）
   * @param {string} content - 输入内容
   * @returns {string} - 八卦结果
   */
  generateBabaResult: function(content) {
    const results = [
      "八卦显示：事情将会顺利进行，不必太过担心。",
      "根据八卦推算：有小困难，但终会化解。",
      "八卦预示：近期会有好消息传来。",
      "卦象显示：需要耐心等待，时机未到。",
      "八卦解析：贵人将会出现，助你一臂之力。",
      "卦象提示：宜静不宜动，静观其变为佳。",
      "八卦预测：有惊无险，最终会有好结果。"
    ];

    // 根据输入内容生成一个伪随机数
    let seed = 0;
    for (let i = 0; i < content.length; i++) {
      seed += content.charCodeAt(i);
    }

    const index = seed % results.length;
    return results[index];
  },

  /**
   * 保存到历史记录
   * @param {string} content - 输入内容
   * @param {string} result - 八卦结果
   * @param {string} id - 记录ID，可选
   * @param {string} createTime - 创建时间，可选
   */
  saveToHistory: function(content, result, id, createTime) {
    // 格式化时间
    let time;
    if (createTime) {
      // 尝试解析后端返回的时间
      try {
        const date = new Date(createTime);
        time = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      } catch (e) {
        console.error('时间解析错误', e);
        // 如果解析失败，使用当前时间
        const now = new Date();
        time = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      }
    } else {
      // 使用当前时间
      const now = new Date();
      time = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    }

    const newHistory = {
      id: id || Date.now().toString(),
      content: content,
      result: result,
      time: time,
      fromServer: !!id // 标记是否来自服务器
    };

    // 获取现有历史记录
    const historyList = this.data.historyList;

    // 检查是否已存在相同ID的记录
    const existingIndex = historyList.findIndex(item => item.id === newHistory.id);
    if (existingIndex !== -1) {
      // 如果已存在，则更新
      historyList[existingIndex] = newHistory;
    } else {
      // 添加新记录到开头
      historyList.unshift(newHistory);

      // 最多保存10条记录
      if (historyList.length > 10) {
        historyList.pop();
      }
    }

    // 更新数据
    this.setData({
      historyList: historyList
    });

    // 保存到本地存储
    wx.setStorageSync('babaHistory', historyList);
  },

  /**
   * 获取历史记录
   */
  getHistoryList: function() {
    const historyList = wx.getStorageSync('babaHistory') || [];
    this.setData({
      historyList: historyList
    });
  },

  /**
   * 查看历史记录详情
   * @param {Object} e - 事件对象
   */
  viewHistoryDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const history = this.data.historyList.find(item => item.id === id);

    if (history) {
      this.setData({
        inputContent: history.content,
        inputLength: history.content.length,
        showResult: true,
        babaResult: history.result
      });

      // 滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
  },



  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    let title = '八八卦 - 算一卦，知未来';
    let path = '/pages/index/index';

    // 如果有结果，分享结果
    if (this.data.showResult) {
      title = `【八八卦】${this.data.babaResult}`;
    }

    return {
      title: title,
      path: path
    };
  }
});