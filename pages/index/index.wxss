/* 容器样式 */
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    padding: 20rpx;
}

/* 八八一下功能区样式 */
.baba-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.baba-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  text-align: center;
}

.baba-input-area {
  position: relative;
  margin-bottom: 20rpx;
}

.baba-input {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.baba-counter {
  position: absolute;
  right: 20rpx;
  bottom: 10rpx;
  font-size: 24rpx;
  color: #999;
}

.baba-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 40rpx;
}

/* 八卦结果区样式 */
.result-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  text-align: center;
}

.result-content {
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  min-height: 100rpx;
}

.result-content text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.result-actions {
  display: flex;
  justify-content: space-between;
}

.action-btn {
  flex: 1;
  margin: 0 10rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
}

.share-btn {
  background-color: #ff9800;
  color: #fff;
}

.save-btn {
  background-color: #2196f3;
  color: #fff;
}

/* 历史记录区样式 */
.history-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.history-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  text-align: center;
}

.history-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.history-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-time {
  font-size: 24rpx;
  color: #999;
}

.empty-tip {
  text-align: center;
  padding: 50rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 授权弹窗样式 */
.auth-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999; /* 提高z-index确保在最上层 */
    display: flex;
    justify-content: center;
    align-items: center;
}

.auth-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7); /* 加深背景色 */
    z-index: 9998;
}

.auth-dialog {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    padding: 40rpx 30rpx;
    border-radius: 24rpx 24rpx 0 0;
    z-index: 10000; /* 确保在mask之上 */
    animation: slideUp 0.3s ease;
    max-height: 60vh; /* 限制最大高度 */
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

.auth-title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
}

.auth-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40rpx;
}

.auth-content text {
    margin: 10rpx 0;
    font-size: 28rpx;
    color: #333;
}

.auth-tips {
    font-size: 24rpx;
    color: #999;
    margin-top: 20rpx;
}

.auth-buttons {
    display: flex;
    justify-content: space-around;
    gap: 0rpx;
}

.auth-cancel,
.auth-confirm {
    flex: 0 0 260rpx;
    /* 不再占满，但在撑不下时可收缩 */
}

.auth-cancel {
    background: #f0f0f0;
    color: #666;
    font-weight: 500;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
    transition: background 0.2s;
}

.auth-cancel:hover {
    background: #e0e0e0;
}

.auth-confirm {
    background: #07c160;
    color: #fff;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
    transition: background 0.2s;
}

.auth-confirm:hover {
    background: #06b35a;
}