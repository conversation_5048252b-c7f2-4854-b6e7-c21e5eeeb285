// app.js

App({
  onLaunch() {
    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    // 从本地存储获取用户信息和token
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')

    console.log('检查登录状态:', { userInfo, token })

    if (userInfo && token) {
      // 已登录
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      console.log('用户已登录')
    } else {
      // 未登录
      this.globalData.isLoggedIn = false
      this.globalData.userInfo = null
      console.log('用户未登录')
    }
  },

  // 获取IP地址
  getIPAddress(callback) {
    wx.getLocalIPAddress({
      success: (res) => {
        const localip = res.localip
        console.log('IP地址:', localip)
        this.globalData.ipAddress = localip
        if (callback) callback(localip)
      },
      fail: (err) => {
        console.error('获取IP地址失败:', err)
        if (callback) callback(null)
      }
    })
  },

  globalData: {
    userInfo: null,
    isLoggedIn: false,
    ipAddress: null
  }
})
