// utils/api.js
const app = getApp()
const API = require('../constants/api')
// 引入schema定义，仅作为文档参考
require('../utils/schema')

/**
 * 封装微信请求API
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求地址
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {boolean} options.needToken - 是否需要token
 * @returns {Promise<Object>} - 返回Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取完整URL
    const url = /^https?:\/\//.test(options.url)
      ? options.url
      : `${API.BASE_URL}${options.url}`

    // 请求头
    const header = options.header || {}

    // 如果需要token，添加到请求头
    if (options.needToken !== false) {
      const token = wx.getStorageSync('token')
      const tokenType = wx.getStorageSync('token_type') || 'Bearer'
      if (token) {
        header['Authorization'] = `${tokenType} ${token}`
      }
    }

    // 发起请求
    wx.request({
      url,
      method: options.method || 'GET',
      data: options.data,
      header,
      success: (res) => {
        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data)
        } else if (res.statusCode === 401) {
          // token失效，需要重新登录
          wx.removeStorageSync('token')
          wx.removeStorageSync('userInfo')
          app.globalData.isLoggedIn = false

          // 跳转到首页重新登录
          wx.reLaunch({
            url: '/pages/index/index'
          })

          reject(new Error('登录已过期，请重新登录'))
        } else {
          // 其他错误
          reject(new Error(res.data.message || '请求失败'))
        }
      },
      fail: () => {
        // 请求失败
        reject(new Error('网络错误，请稍后再试'))
      }
    })
  })
}

/**
 * 微信登录API
 * @param {WechatLoginRequest} data - 登录数据
 * @returns {Promise<WechatLoginResponse>} - 返回Promise对象
 */
const login = (data) => {
  return request({
    url: API.AUTH.WECHAT_LOGIN,
    method: 'POST',
    data,
    needToken: false
  })
}

/**
 * 获取用户信息API
 * @returns {Promise<UserInfoResponse>} - 返回Promise对象
 */
const getUserInfo = () => {
  return request({
    url: API.USER.INFO,
    method: 'GET'
  })
}

/**
 * 提交八八一下请求
 * @param {BabaRequest} data - 八八一下请求数据
 * @returns {Promise<BabaResponse>} - 返回Promise对象
 */
const submitBaba = (data) => {
  return request({
    url: API.BABA.SUBMIT,
    method: 'POST',
    data,
    needToken: true
  })
}

/**
 * 获取八八一下历史记录
 * @returns {Promise<Object>} - 返回Promise对象
 */
const getBabaHistory = () => {
  return request({
    url: API.BABA.HISTORY,
    method: 'GET',
    needToken: true
  })
}



/**
 * 问卦查询
 * @param {GuaQueryRequest} data - 问卦请求数据
 * @returns {Promise<GuaQueryResponse>} - 返回Promise对象
 */
const queryGua = (data) => {
  return request({
    url: API.GUA.QUERY,
    method: 'POST',
    data,
    needToken: true
  })
}

module.exports = {
  request,
  login,
  getUserInfo,
  submitBaba,
  getBabaHistory,
  queryGua
}
