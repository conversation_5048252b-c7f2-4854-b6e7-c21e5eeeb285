// utils/schema.js
/**
 * 该文件用于定义API请求和响应的schema
 * 可以作为接口文档和类型检查的参考
 */

/**
 * 微信用户信息schema
 * @typedef {Object} WechatUserInfo
 * @property {string} nickName - 用户昵称
 * @property {string} avatarUrl - 用户头像URL
 * @property {number} gender - 用户性别(0:未知, 1:男, 2:女)
 * @property {string} country - 用户所在国家
 * @property {string} province - 用户所在省份
 * @property {string} city - 用户所在城市
 * @property {string} language - 用户的语言
 */

/**
 * 页面数据schema
 * @typedef {Object} PageData
 * @property {WechatUserInfo|null} userInfo - 用户信息
 * @property {boolean} hasUserInfo - 是否已有用户信息
 * @property {boolean} canIUseGetUserProfile - 是否可以使用getUserProfile
 * @property {boolean} showAuthDialog - 是否显示授权弹窗
 * @property {string} ipAddress - IP地址
 */

/**
 * 微信登录请求schema
 * @typedef {Object} WechatLoginRequest
 * @property {string} code - 微信登录凭证
 * @property {WechatUserInfo} userInfo - 用户信息
 * @property {string} ipAddress - 用户IP地址
 */

/**
 * 微信登录响应schema
 * @typedef {Object} WechatLoginResponse
 * @property {number} code - 状态码，10200表示成功
 * @property {string} message - 响应消息，如"登录成功"
 * @property {Object} data - 响应数据
 * @property {string} data.access_token - 用户访问令牌
 * @property {number} data.expires_in - 令牌过期时间（秒）
 * @property {string} data.refresh_token - 刷新令牌
 * @property {string} data.token_type - 令牌类型，如"bearer"
 * @property {Object} data.user - 用户信息
 * @property {string} data.user.nickname - 用户昵称
 * @property {string|null} data.user.avatar_url - 用户头像URL
 */

/**
 * 用户信息请求schema
 * @typedef {Object} UserInfoRequest
 * 无需请求参数，通过token鉴权
 */

/**
 * 用户信息响应schema
 * @typedef {Object} UserInfoResponse
 * @property {number} code - 状态码，200表示成功
 * @property {string} message - 响应消息
 * @property {Object} data - 响应数据
 * @property {string} data.openid - 用户openid
 * @property {string} data.unionid - 用户unionid（如果有）
 * @property {WechatUserInfo} data.userInfo - 用户信息
 */

/**
 * 通用响应schema
 * @typedef {Object} CommonResponse
 * @property {number} code - 状态码，200表示成功
 * @property {string} message - 响应消息
 * @property {Object|Array|null} data - 响应数据
 */

/**
 * 错误响应schema
 * @typedef {Object} ErrorResponse
 * @property {number} code - 错误码
 * @property {string} message - 错误消息
 * @property {Object|null} errors - 详细错误信息
 */

/**
 * 八八一下请求schema
 * @typedef {Object} BabaRequest
 * @property {string} content - 用户输入的内容
 * @property {string} ipAddress - 用户IP地址
 * @property {string} credential - 用户凭证
 * @property {string} token - 用户令牌
 */

/**
 * 八八一下响应schema
 * @typedef {Object} BabaResponse
 * @property {number} code - 状态码，10200表示成功
 * @property {string} message - 响应消息
 * @property {Object} data - 响应数据
 * @property {string} data.result - 八卦结果
 * @property {string} data.id - 记录ID
 * @property {string} data.createTime - 创建时间
 */

/**
 * 保存到许愿池请求schema
 * @typedef {Object} SaveToWishPoolRequest
 * @property {string} content - 愿望内容
 * @property {string} babaId - 八八一下记录ID
 * @property {string} credential - 用户凭证
 * @property {string} token - 用户令牌
 */

// 导出schema定义，方便在其他文件中引用
module.exports = {
  // 这里只是为了导出类型定义，实际上不包含任何实现
  // 在使用TypeScript的项目中，可以直接使用类型定义
};
