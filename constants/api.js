// constants/api.js
/**
 * API路径常量定义
 * 集中管理所有API路径，便于维护
 */

// API基础路径
const BASE_URL = 'http://127.0.0.1:8000';

// 认证相关API
const AUTH = {
  // 微信登录
  WECHAT_LOGIN: '/v1/auth/wechat/login',
  // 刷新token
  REFRESH_TOKEN: '/v1/auth/refresh',
  // 登出
  LOGOUT: '/v1/auth/logout'
};

// 用户相关API
const USER = {
  // 获取用户信息
  INFO: '/v1/user/info',
  // 更新用户信息
  UPDATE: '/v1/user/update'
};

// 八八一下相关API
const BABA = {
  // 提交八八一下请求
  SUBMIT: '/v1/baba/submit',
  // 获取历史记录
  HISTORY: '/v1/baba/history',
  // 保存到许愿池
  SAVE_TO_WISHPOOL: '/v1/baba/save-to-wishpool'
};

// 问卦相关API
const GUA = {
  // 问卦查询
  QUERY: '/v1/gua/query'
};

// 其他API路径可以按模块继续添加...

// 导出所有API常量
module.exports = {
  BASE_URL,
  AUTH,
  USER,
  BABA,
  GUA
};
